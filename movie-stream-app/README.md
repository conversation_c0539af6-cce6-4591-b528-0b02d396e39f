# 🎬 CineStream - Professional Movie Streaming Platform

A modern, responsive movie and TV show streaming platform built with Flask and Python. Features TMDB integration, multiple streaming servers, and a beautiful dark theme UI.

## ✨ Features

### 🎯 Core Features
- **Movie & TV Show Browsing** - Browse popular, top-rated, and trending content
- **Advanced Search** - Search movies, TV shows, and actors
- **Detailed Information** - Cast, ratings, genres, and plot summaries
- **Multiple Streaming Servers** - 4 different streaming sources for reliability
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile

### 🎨 UI/UX Features
- **Modern Dark Theme** - Professional Netflix-style interface
- **Smooth Animations** - Hover effects and transitions
- **Loading States** - Professional loading indicators
- **Error Handling** - User-friendly error messages
- **Accessibility** - Screen reader friendly with proper ARIA labels

### 🔧 Technical Features
- **Flask Backend** - Fast and reliable Python web framework
- **TMDB API Integration** - Real movie and TV show data
- **Error Boundaries** - Graceful error handling
- **SEO Optimized** - Proper meta tags and structure
- **Performance Optimized** - Lazy loading and efficient API calls

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   cd movie-stream-app
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser**
   ```
   http://localhost:5000
   ```

## 📱 Usage

### Navigation
- **Home** - Trending and popular content
- **Movies** - Browse movies by category
- **TV Shows** - Browse TV series by category
- **Search** - Find specific content

### Watching Content
1. Click on any movie or TV show poster
2. View detailed information and cast
3. Click "Watch Now" or "Watch S1E1"
4. Choose from multiple streaming servers
5. Enjoy high-quality streaming!

### TV Shows
- Select season and episode numbers
- Navigate between episodes easily
- Auto-play next episode (coming soon)

## 🎛️ Streaming Servers

The platform includes 5 reliable streaming servers:

1. **Embed API Stream** - Premium streaming API, highly reliable
2. **VidSrc Pro** - Primary server, most reliable
3. **VidSrc TO** - Fast with multiple sources
4. **Embed SU** - Reliable backup server
5. **MultiEmbed** - Multiple streaming sources

If one server doesn't work, simply try another!

## 🔧 Configuration

### API Key
The TMDB API key is included for demo purposes. For production use:

1. Get your own API key from [TMDB](https://www.themoviedb.org/settings/api)
2. Replace the API key in `app.py`:
   ```python
   TMDB_API_KEY = 'your-api-key-here'
   ```

### Server Configuration
You can modify streaming servers in `app.py`:
```python
STREAMING_SERVERS = [
    {
        'id': 'server_id',
        'name': 'Server Name',
        'description': 'Server description',
        'base_url': 'https://server-url.com'
    }
]
```

## 📁 Project Structure

```
movie-stream-app/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── templates/            # HTML templates
    ├── base.html         # Base template with navigation
    ├── home.html         # Homepage
    ├── movies.html       # Movies listing
    ├── tv.html           # TV shows listing
    ├── movie_details.html # Movie details page
    ├── tv_details.html   # TV show details page
    ├── watch.html        # Streaming player page
    ├── search.html       # Search results
    └── error.html        # Error page
```

## 🎨 Customization

### Themes
The CSS uses CSS variables for easy theming:
```css
:root {
    --primary-color: #e50914;    /* Netflix red */
    --dark-bg: #0a0a0a;          /* Main background */
    --dark-secondary: #141414;    /* Card backgrounds */
    --text-primary: #ffffff;      /* Main text */
    --text-secondary: #b3b3b3;    /* Secondary text */
}
```

### Adding New Pages
1. Create a new route in `app.py`
2. Create a new template in `templates/`
3. Add navigation links in `base.html`

## 🔒 Security Notes

- API keys should be moved to environment variables in production
- Add rate limiting for API calls
- Implement user authentication if needed
- Use HTTPS in production

## 🐛 Troubleshooting

### Common Issues

**Movies not loading:**
- Check internet connection
- Try different streaming servers
- Check browser console for errors

**API errors:**
- Verify TMDB API key is valid
- Check API rate limits
- Ensure internet connectivity

**Streaming issues:**
- Try different servers
- Check if content is available
- Disable ad blockers if necessary

## 📈 Performance Tips

- Use a CDN for static assets in production
- Implement caching for API responses
- Add database for user preferences
- Use a production WSGI server like Gunicorn

## 🤝 Contributing

Feel free to contribute by:
- Adding new streaming servers
- Improving the UI/UX
- Adding new features
- Fixing bugs
- Improving documentation

## 📄 License

This project is for educational purposes. Please respect copyright laws and streaming service terms of use.

## 🙏 Credits

- **TMDB** - Movie and TV show data
- **Bootstrap** - CSS framework
- **Font Awesome** - Icons
- **Google Fonts** - Typography

---

**Enjoy streaming! 🍿**
